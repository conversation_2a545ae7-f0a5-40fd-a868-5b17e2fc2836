/**
 * Enhanced Configuration System for Energy.AI
 * نظام التكوين المحسن لموقع Energy.AI
 */

class ConfigSystem {
    constructor() {
        this.config = {
            // API Configuration
            api: {
                baseURL: this.getAPIBaseURL(),
                timeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                endpoints: {
                    chat: '/api/chat',
                    auth: '/api/auth',
                    contact: '/api/contact',
                    analytics: '/api/analytics',
                    security: '/api/security'
                }
            },

            // Performance Configuration
            performance: {
                enableServiceWorker: true,
                enableAnalytics: true,
                enableNotifications: true,
                enableSecurity: true,
                lazyLoadImages: true,
                preloadCriticalResources: true,
                cacheStrategy: 'cache-first',
                maxCacheAge: 7 * 24 * 60 * 60 * 1000, // 7 days
                batchAnalyticsEvents: true,
                analyticsFlushInterval: 30000
            },

            // UI Configuration
            ui: {
                theme: localStorage.getItem('theme') || 'dark',
                language: localStorage.getItem('website-language') || 'en',
                animations: !this.prefersReducedMotion(),
                notifications: {
                    position: 'top-right',
                    maxVisible: 3,
                    defaultDuration: 5000,
                    enableSound: false
                },
                chat: {
                    enableVoice: true,
                    enableTypingIndicator: true,
                    maxMessageLength: 1000,
                    enableEmojis: true
                }
            },

            // Security Configuration
            security: {
                enableCSRF: true,
                enableRateLimiting: true,
                enableInputValidation: true,
                enableXSSProtection: true,
                maxLoginAttempts: 3,
                sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
                encryptLocalStorage: true
            },

            // Feature Flags
            features: {
                enableAIChat: true,
                enableVoiceAssistant: true,
                enableMaps: true,
                enableDarkMode: true,
                enableLanguageSwitching: true,
                enablePWA: true,
                enableOfflineMode: true,
                enablePushNotifications: false, // Disabled by default
                enableBiometricAuth: false, // Future feature
                enableAdvancedAnalytics: true
            },

            // External Services
            external: {
                googleMaps: {
                    apiKey: '', // Set in environment
                    defaultZoom: 10,
                    defaultCenter: { lat: 31.9539, lng: 35.9106 } // Amman, Jordan
                },
                geminiAI: {
                    apiKey: 'AIzaSyDplyiq89Ax8494spN6d7czLNnoloGaYQM',
                    model: 'gemini-pro',
                    temperature: 0.7,
                    maxTokens: 1024
                },
                analytics: {
                    enableGoogleAnalytics: false,
                    googleAnalyticsId: '',
                    enableCustomAnalytics: true
                }
            },

            // Development Configuration
            development: {
                enableDebugMode: this.isDevelopment(),
                enableConsoleLogging: this.isDevelopment(),
                enablePerformanceLogging: this.isDevelopment(),
                mockAPIResponses: false,
                bypassRateLimiting: this.isDevelopment()
            }
        };

        this.init();
    }

    init() {
        this.loadEnvironmentConfig();
        this.validateConfig();
        this.setupConfigWatchers();
        console.log('⚙️ Configuration System initialized');
    }

    getAPIBaseURL() {
        const hostname = window.location.hostname;
        const protocol = window.location.protocol;

        // Local development or opened directly from file system
        if (protocol === 'file:' || hostname === 'localhost' || hostname === '127.0.0.1') {
            return 'http://localhost:5000';
        }

        if (hostname.includes('netlify.app') || hostname.includes('vercel.app')) {
            return 'https://energy-ai-backend-gemini-i30n5wt6k-mohammad-basims-projects.vercel.app';
        }

        return window.location.origin;
    }

    isDevelopment() {
        return window.location.hostname === 'localhost' || 
               window.location.hostname === '127.0.0.1' ||
               window.location.protocol === 'file:';
    }

    prefersReducedMotion() {
        return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }

    loadEnvironmentConfig() {
        // Load configuration from meta tags or environment
        const configMeta = document.querySelector('meta[name="app-config"]');
        if (configMeta) {
            try {
                const envConfig = JSON.parse(configMeta.content);
                this.mergeConfig(envConfig);
            } catch (error) {
                console.warn('Failed to parse environment config:', error);
            }
        }

        // Load from localStorage overrides
        const savedConfig = localStorage.getItem('energy-ai-config');
        if (savedConfig) {
            try {
                const userConfig = JSON.parse(savedConfig);
                this.mergeConfig(userConfig);
            } catch (error) {
                console.warn('Failed to parse saved config:', error);
            }
        }
    }

    mergeConfig(newConfig) {
        this.config = this.deepMerge(this.config, newConfig);
    }

    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(target[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }

    validateConfig() {
        // Validate critical configuration
        const requiredKeys = [
            'api.baseURL',
            'ui.theme',
            'ui.language',
            'security.enableCSRF'
        ];

        for (const key of requiredKeys) {
            if (!this.get(key)) {
                console.error(`Missing required configuration: ${key}`);
            }
        }

        // Validate API endpoints
        if (!this.config.api.baseURL) {
            console.error('API base URL is not configured');
        }

        // Validate feature flags
        if (this.config.features.enableAIChat && !this.config.external.geminiAI.apiKey) {
            console.warn('AI Chat is enabled but no API key is configured');
        }
    }

    setupConfigWatchers() {
        // Watch for theme changes
        const themeObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.attributeName === 'data-theme') {
                    const newTheme = document.documentElement.getAttribute('data-theme');
                    this.set('ui.theme', newTheme);
                }
            });
        });

        themeObserver.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });

        // Watch for language changes
        document.addEventListener('languageChanged', (e) => {
            this.set('ui.language', e.detail.language);
        });
    }

    // Get configuration value
    get(key) {
        return key.split('.').reduce((obj, k) => obj && obj[k], this.config);
    }

    // Set configuration value
    set(key, value) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, k) => {
            if (!obj[k]) obj[k] = {};
            return obj[k];
        }, this.config);
        
        target[lastKey] = value;
        
        // Save to localStorage
        this.saveConfig();
        
        // Emit configuration change event
        document.dispatchEvent(new CustomEvent('configChanged', {
            detail: { key, value }
        }));
    }

    // Save configuration to localStorage
    saveConfig() {
        try {
            const configToSave = {
                ui: this.config.ui,
                features: this.config.features,
                performance: this.config.performance
            };
            localStorage.setItem('energy-ai-config', JSON.stringify(configToSave));
        } catch (error) {
            console.error('Failed to save configuration:', error);
        }
    }

    // Reset configuration to defaults
    reset() {
        localStorage.removeItem('energy-ai-config');
        window.location.reload();
    }

    // Get all configuration
    getAll() {
        return { ...this.config };
    }

    // Check if feature is enabled
    isFeatureEnabled(feature) {
        return this.get(`features.${feature}`) === true;
    }

    // Get API endpoint URL
    getAPIEndpoint(endpoint) {
        const baseURL = this.get('api.baseURL');
        const endpointPath = this.get(`api.endpoints.${endpoint}`);
        return baseURL + endpointPath;
    }

    // Get environment-specific configuration
    getEnvironmentConfig() {
        return {
            isDevelopment: this.isDevelopment(),
            apiBaseURL: this.get('api.baseURL'),
            enableDebugMode: this.get('development.enableDebugMode'),
            enableConsoleLogging: this.get('development.enableConsoleLogging')
        };
    }

    destroy() {
        this.saveConfig();
        console.log('⚙️ Configuration System destroyed');
    }
}

// Initialize configuration system
document.addEventListener('DOMContentLoaded', () => {
    window.configSystem = new ConfigSystem();
});

// Export for use in other modules
window.ConfigSystem = ConfigSystem;
