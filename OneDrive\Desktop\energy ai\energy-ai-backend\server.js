const express = require('express');
const sqlite3 = require('sqlite3').verbose();
const cors = require('cors');
const https = require('https');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 5000;

app.use(cors());
app.use(express.json());

// Database setup
const path = require('path');
const dbPath = path.join(__dirname, 'energy-ai.db');
const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error(err.message);
    }
    console.log('Connected to the energy-ai database.');
});

// Health check
app.get('/api/health', (req, res) => {
    res.json({ status: 'ok', service: 'energy-ai-backend', time: Date.now() });
});

// API endpoints
// Root API info (to avoid "Cannot GET /api")
app.get('/api', (req, res) => {
    res.json({
        status: 'ok',
        service: 'energy-ai-backend',
        endpoints: {
            health: 'GET /api/health',
            data: 'GET /api/data',
            chat: 'POST /api/chat',
            analytics: 'POST /api/analytics',
            securityReport: 'POST /api/security/report'
        }
    });
});
app.get('/api/data', (req, res) => {
    db.all("SELECT * FROM energy_data", [], (err, rows) => {
        if (err) {
            res.status(400).json({ "error": err.message });
            return;
        }
        res.json({
            "message": "success",
            "data": rows
        });
    });
});

// Lightweight stubs for local development
app.post('/api/analytics', (req, res) => {
    // Accept analytics events without storing (dev stub)
    res.status(204).end();
});

app.post('/api/security/report', (req, res) => {
    // Accept security reports without action (dev stub)
    res.status(204).end();
});

// Admin authentication endpoint
app.post('/api/admin/auth', (req, res) => {
    const { username, password } = req.body;

    if (username === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
        res.json({
            "message": "Admin authentication successful",
            "success": true
        });
    } else {
        res.status(401).json({
            "message": "Admin authentication failed",
            "success": false
        });
    }
});

// Helper: call OpenAI Chat Completions API without extra deps
function callOpenAIChat(payload) {
    return new Promise((resolve, reject) => {
        const data = JSON.stringify(payload);
        const options = {
            hostname: 'api.openai.com',
            path: '/v1/chat/completions',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
                'Content-Length': Buffer.byteLength(data)
            }
        };

        const req = https.request(options, (resp) => {
            let body = '';
            resp.on('data', (chunk) => { body += chunk; });
            resp.on('end', () => {
                try {
                    const json = JSON.parse(body);
                    if (resp.statusCode && resp.statusCode >= 200 && resp.statusCode < 300) {
                        resolve(json);
                    } else {
                        const err = new Error(json.error?.message || `OpenAI error: ${resp.statusCode}`);
                        err.status = resp.statusCode;
                        err.details = json;
                        reject(err);
                    }
                } catch (e) {
                    reject(e);
                }
            });
        });
        req.on('error', reject);
        req.write(data);
        req.end();
    });
}

// Chat endpoint (OpenAI proxy)
app.post('/api/chat', async (req, res) => {
    try {
        if (!process.env.OPENAI_API_KEY) {
            return res.status(500).json({ error: 'Missing OPENAI_API_KEY in environment.' });
        }

        const { messages = [], temperature = 0.7, max_tokens = 300 } = req.body || {};
        // Force a safe default model; ignore client override to avoid access errors
        const model = process.env.OPENAI_DEFAULT_MODEL || 'gpt-4o-mini';

        // Minimal system prompt specialized for Energy Optimization
        const systemMessage = {
            role: 'system',
            content: 'أنت مساعد متخصص في تحسين الطاقة للمنشآت التجارية والصناعية. اجعل الأولويات: خفض التكلفة، ثم خفض الانبعاثات، مع احترام قيود الراحة والسلامة. أجب باختصار وبخطوات واضحة، واطلب البيانات الناقصة دون تخمين.'
        };

        const payload = {
            model,
            messages: [
                {
                    role: 'system',
                    content: (
                        'You are Energy.AI Assistant focused on Energy Optimization. '
                        + 'Goals: minimize energy cost, reduce waste, and improve efficiency for homes and businesses. '
                        + 'Behavior: ask one clarifying question at a time when needed; give concise, actionable steps; show quick calculations where useful; '
                        + 'adapt to Jordan/MENA tariffs and context when relevant. If data is missing (bill amount, kWh usage, tariff, peak/off-peak, building type, solar potential, location), ask for it. '
                        + 'Format answers with short paragraphs and bullet points. Keep it safe and practical.'
                        + '\n\nArabic guidance: كن مساعد Energy.AI مختص بتحسين استهلاك الطاقة. '
                        + 'الهدف: تخفيض الفاتورة وتقليل الهدر ورفع الكفاءة في المنازل والشركات. '
                        + 'الأسلوب: اطرح سؤال توضيحي واحد عند الحاجة، قدم خطوات عملية قصيرة مع أرقام تقريبية عند الإمكان، '
                        + 'وإذا نقصت البيانات (قيمة الفاتورة، الاستهلاك بالكيلوواط ساعة، التعرفة، ذروة/خارج الذروة، نوع المبنى، إمكانية الطاقة الشمسية، الموقع) فاسأل عنها. '
                        + 'نسّق الإجابات بنقاط مختصرة وسهلة المتابعة.'
                    )
                },
                ...messages
            ],
            temperature,
            max_tokens,
            stream: false
        };

        const oaiResponse = await callOpenAIChat(payload);

        // Return in OpenAI-compatible shape expected by frontend
        return res.json(oaiResponse);
    } catch (err) {
        console.error('Chat endpoint error:', err);
        return res.status(err.status || 500).json({
            error: err.message || 'Chat failed',
            details: err.details || null
        });
    }
});

// Start server
app.listen(port, () => {
    console.log(`Server running on port ${port}`);
});
