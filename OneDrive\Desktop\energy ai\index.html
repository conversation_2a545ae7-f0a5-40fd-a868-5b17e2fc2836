<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems. Advanced renewable energy solutions for businesses and homes in Jordan and MENA region.">
    <parameter name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy, MENA energy solutions, solar power, wind energy, smart grid, IoT energy, energy analytics, carbon footprint reduction">
    <meta name="author" content="Energy.AI - Mohammad <PERSON>">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="language" content="en">
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Energy.AI">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes. Advanced renewable energy solutions with real-time monitoring and predictive analytics.">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:image:alt" content="Energy.AI Logo - Smart Energy Solutions">
    <meta property="og:site_name" content="Energy.AI">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://energy-ai.netlify.app/">
    <meta name="twitter:title" content="Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Advanced renewable energy management systems.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:image:alt" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:creator" content="@EnergyAI_Jordan">
    <meta name="twitter:site" content="@EnergyAI_Jordan">

    <!-- Canonical and Alternate Languages -->
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="en" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="ar" href="https://energy-ai.netlify.app/?lang=ar">
    <link rel="alternate" hreflang="x-default" href="https://energy-ai.netlify.app/">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <title>Energy.AI - Smart Energy Solutions | AI-Powered Energy Optimization</title>

    <!-- Modern Glassmorphism CSS - Inline for performance -->
    <style>
        /* Modern Glassmorphism Critical Styles */
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        [data-theme="light"] {
            --background-primary: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
            --text-primary: #2c3e50;
            --text-secondary: #5d4e37;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.3);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
            transition: all var(--transition-normal);
        }

        /* Glassmorphism Background */
        .glassmorphism-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--background-primary);
        }

        /* Floating Shapes */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }

        /* Glass Components */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .main {
            min-height: 100vh;
            position: relative;
        }

        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all var(--transition-normal);
        }

        /* Critical inline styles for immediate loading */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Energy Optimization Card Styles */
        .energy-optimization-card {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .energy-optimization-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(25, 118, 210, 0.3);
        }

        .card-action {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .energy-optimization-card:hover .card-action {
            opacity: 1;
            transform: translateY(0);
        }

        .action-text {
            font-size: 14px;
            color: #1976d2;
            font-weight: 500;
        }

        .action-arrow {
            font-size: 18px;
            color: #1976d2;
            transition: transform 0.3s ease;
        }

        .energy-optimization-card:hover .action-arrow {
            transform: translateX(5px);
        }

        /* Energy Optimization Modal Styles */
        .energy-optimization-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: none;
            opacity: 0;
            transition: all 0.3s ease;
        }

        

        .energy-optimization-modal.active {
            display: flex;
            opacity: 1;
        }

        .modal-container {
            display: block;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 30%, #16213e 50%, #0f3460 70%, #533483 100%);
            position: relative;
            overflow: hidden;
        }

        .modal-sidebar {
            width: 280px;
            background: transparent;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 0;
            left: 0;
            height: 100vh;
            transform: translateX(-270px);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
            z-index: 10001;
            opacity: 0;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.5);
        }

        .modal-sidebar::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 8px;
            height: 40px;
            background: rgba(25, 118, 210, 0.05);
            border-radius: 0 6px 6px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s ease;
            opacity: 0.3;
        }

        .modal-sidebar::before {
            content: '⋮';
            position: absolute;
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
            color: rgba(25, 118, 210, 0.4);
            font-size: 12px;
            z-index: 1001;
            pointer-events: none;
            transition: all 0.4s ease;
            opacity: 0.5;
        }

        .modal-sidebar:hover {
            transform: translateX(0);
            opacity: 1;
        }

        .modal-sidebar:hover::after {
            background: rgba(25, 118, 210, 0.15);
            width: 12px;
            opacity: 0.8;
        }

        .modal-sidebar:hover::before {
            opacity: 0;
            transform: translateY(-50%) scale(0.5);
        }

        .sidebar-hover-area {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 100vh;
            z-index: 10000;
            background: transparent;
        }









        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .modal-sidebar {
                transform: translateX(-290px);
                opacity: 0;
                width: 90vw;
                max-width: 320px;
            }

            .sidebar-hover-area {
                width: 15px;
            }

            .modal-sidebar::after {
                right: -6px;
                width: 6px;
                height: 30px;
                opacity: 0.2;
            }

            .modal-sidebar::before {
                right: -4px;
                font-size: 10px;
                opacity: 0.3;
            }

            .modal-main-content {
                width: 100vw;
                height: 100vh;
            }

            .modal-chat-area {
                padding: 0;
                height: calc(100vh - 100px);
            }

            .chat-content-area {
                padding: 20px 15px;
                max-width: 95%;
            }

            .messages-container {
                padding: 20px 15px;
                max-width: 95%;
            }

            .message {
                max-width: 95%;
                width: 95%;
            }

            .modal-input-wrapper {
                max-width: 95%;
                width: 95%;
            }
        }

        .modal-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .modal-logo-text {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #ffffff;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 10001;
        }

        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .sidebar-close-section {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-close-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            direction: rtl;
        }

        .sidebar-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 1);
        }

        .sidebar-close-btn svg {
            width: 14px;
            height: 14px;
        }

        .fixed-energy-title {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 12px 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
        }

        .energy-title-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
        }

        .fixed-energy-title h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .new-chat-btn {
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-chat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
        }

        .chat-item {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .chat-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .chat-item.active {
            background: rgba(25, 118, 210, 0.15);
            border-color: rgba(25, 118, 210, 0.3);
        }

        .chat-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .chat-preview {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .modal-main-content {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            position: relative;
        }

        .modal-header {
            padding: 80px 32px 24px;
            border-bottom: none;
            background: transparent;
        }

        .modal-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
        }

        .modal-header p {
            color: #888;
            font-size: 14px;
        }

        .modal-chat-area {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            scroll-behavior: smooth;
            display: flex;
            flex-direction: column;
            position: relative;
            width: 100%;
            height: calc(100vh - 120px);
            align-items: center;
            justify-content: center;
        }

        /* Reserve space for the fixed bottom input bar so messages do not hide underneath */
        .modal-chat-area { padding-bottom: 220px; }

        .chat-content-area {
            text-align: center;
            margin: auto;
            max-width: 800px;
            width: 90%;
            padding: 40px 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            min-height: 60vh;
        }

        /* Ensure messages never hide behind the bottom input bar */
        .chat-content-area,
        .messages-container,
        .messages-list {
            padding-bottom: 180px; /* space for fixed input bar */
        }

        .welcome-content {
            display: flex !important;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 10;
        }

        .welcome-icon {
            display: none;
        }

        .welcome-title {
            display: block !important;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 32px;
            text-align: center;
            color: #ffffff;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4);
            visibility: visible !important;
            opacity: 1 !important;
        }

        .welcome-subtitle {
            color: #888;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.5;
        }



        .messages-container {
            display: none;
            flex-direction: column;
            gap: 32px;
            padding: 40px 20px;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            width: 100%;
            max-width: 900px;
            margin: 0 auto;
            flex: 1;
            justify-content: flex-start;
            align-items: center;
        }

        .message {
            display: flex;
            gap: 0px;
            max-width: 800px;
            width: 90%;
            margin: 24px auto;
            align-items: center;
            justify-content: center;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #1976d2, #ff7200);
        }

        .message.user .message-avatar {
            background: rgba(255, 255, 255, 0.1);
        }

        .message-content {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 20px;
            padding: 24px 32px;
            line-height: 1.7;
            color: white;
            font-size: 15px;
            width: 100%;
            max-width: 700px;
            word-wrap: break-word;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            text-align: center;
            margin: 0 auto;
        }

        .message.user .message-content {
            background: rgba(25, 118, 210, 0.1);
            border-color: rgba(25, 118, 210, 0.2);
        }

        .modal-input-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .modal-input-container.center-input {
            padding: 40px 20px;
            margin: 40px 0;
            position: relative;
        }

        .modal-input-container.bottom-input {
            padding: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.02);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .modal-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: rgba(20, 20, 20, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            padding: 12px;
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 700px;
            transition: all 0.3s ease;
        }

        .modal-input-field {
            flex: 1;
            background: transparent;
            border: none;
            resize: none;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 15px;
            line-height: 1.4;
            outline: none;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            min-height: 20px;
            max-height: 120px;
            overflow-y: auto;
            direction: rtl;
            text-align: center;
        }

        .modal-input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
            margin: 0 4px;
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            background: rgba(25, 118, 210, 0.3);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.9);
            margin-left: 4px;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.5);
            transform: scale(1.05);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            position: static;
            transform: none;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.3);
            color: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }


        @media (max-width: 768px) {
            .modal-container {
                flex-direction: column;
            }

            .modal-sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }


        }
    </style>

    <!-- Critical CSS - Load immediately -->
    <link rel="stylesheet" href="css/glassmorphism.css">
    <link rel="stylesheet" href="css/welcome-screen.css">

    <!-- CSS Files - Load non-critical CSS asynchronously -->
    <link rel="preload" href="css/performance-optimizations.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/space-background.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/naya.css">
    <link rel="stylesheet" href="css/enhanced-map-animations.css">
    <link rel="stylesheet" href="css/neon-cursor.css">

    <!-- Fallback for browsers that don't support preload -->
    <noscript>
        <link rel="stylesheet" href="css/performance-optimizations.css">
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/space-background.css">
        <link rel="stylesheet" href="css/welcome-screen.css">
    </noscript>


    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Glassmorphism Background -->
    <div class="glassmorphism-bg"></div>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo large-logo"></div>
                    <div class="logo-glow"></div>
                </div>
                <h1 class="welcome-title">Welcome to</h1>
                <h2 class="brand-name">Energy.AI</h2>
                <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="energy-particles"></div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main">
        <nav class="navbar glass">
            <div class="nav-content">
                <a href="#home" class="nav-logo">
                    <div class="nav-logo-icon">⚡</div>
                    <span>Energy.AI</span>
                </a>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#about" class="nav-link">About</a></li>
                    <li><a href="#service" class="nav-link">Services</a></li>
                    <li><a href="#design" class="nav-link">Design</a></li>
                    <li><a href="#contact" class="nav-link">Contact</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <ion-icon name="menu-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </nav>
        <section class="hero" id="home">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line-1">Web Design &</span>
                    <span class="title-line-2">Development</span>
                    <span class="title-line-3">Energy</span>
                </h1>
                <p class="hero-description">
                    AI is the spark igniting a new era of energy innovation<br>
                    powering tomorrow with<br>
                    intelligent solutions today
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary" id="joinBtn">
                        <ion-icon name="person-add-outline"></ion-icon>
                        Join Us
                    </button>
                    <button class="btn btn-secondary">Book Free Consultation</button>
                </div>
            </div>

            <!-- Floating Glass Elements -->
            <div class="floating-glass floating-glass-1"></div>
            <div class="floating-glass floating-glass-2"></div>
            <div class="floating-glass floating-glass-3"></div>
        </section>

        <!-- Auth Modal will be created by auth-system.js -->

        <!-- Fixed CTA Button -->
        <div class="fixed-cta-container">
            <button class="fixed-cta-btn" id="fixedCtaBtn">
                <ion-icon name="call-outline"></ion-icon>
                <span>Get Free Consultation</span>
            </button>
        </div>

        <!-- AI Chat Interface -->
        <div class="ai-chat-container" id="aiChatContainer">
            <div class="chat-header">
                <h3>Energy.AI Assistant</h3>
                <div class="chat-controls">
                    <button class="minimize-btn chat-control-btn" id="minimizeChatBtn">
                        <ion-icon name="remove-outline"></ion-icon>
                    </button>
                    <button class="close-btn chat-control-btn" id="closeChatBtn">
                        <ion-icon name="close-outline"></ion-icon>
                    </button>
                </div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="avatar">
                        <div class="avatar-icon-wrapper">
                            <ion-icon name="analytics-outline"></ion-icon>
                        </div>
                    </div>
                    <div class="message-content">Hello, I'm Energy.AI Assistant</div>
                </div>
            </div>
            <div class="suggested-questions">
                <button class="question-btn">How can AI improve energy efficiency?</button>
                <button class="question-btn">What is renewable energy?</button>
                <button class="question-btn">How to reduce electricity bills?</button>
            </div>
            <div class="chat-input">
                <input type="text" id="userInput" placeholder="Type your message here..." aria-label="Message input">
                <button id="sendAttachmentBtn" class="chat-icon-btn" aria-label="Send attachment">
                    <ion-icon name="attach-outline"></ion-icon>
                </button>
                <button id="sendMessageBtn" class="chat-send-btn">
                    <ion-icon name="send-outline"></ion-icon>
                    <span>Send</span>
                </button>
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <section id="about" class="section">
            <h2>About Energy.AI</h2>
            <div class="cards-grid">
                <div class="glass-card">
                    <div class="card-icon">⚡</div>
                    <h3>Smart Energy Management</h3>
                    <p>AI-powered solutions to optimize energy consumption in real-time.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🌱</div>
                    <h3>Sustainable Solutions</h3>
                    <p>Eco-friendly approaches to energy production and distribution.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📊</div>
                    <h3>Data-Driven Insights</h3>
                    <p>Comprehensive analytics to make informed energy decisions.</p>
                </div>
            </div>
        </section>

        <section id="service" class="section">
            <h2>Our Services</h2>
            <div class="cards-grid">
                <div class="glass-card energy-optimization-card" onclick="openEnergyOptimization()">
                    <div class="card-icon">💡</div>
                    <h3>Energy Optimization</h3>
                    <p>Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI consultation</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📈</div>
                    <h3>Predictive Maintenance</h3>
                    <p>Prevent equipment failures before they happen with our predictive maintenance solutions powered by machine learning.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI consultation</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🛡️</div>
                    <h3>Energy Security</h3>
                    <p>Protect your energy infrastructure with advanced threat detection and response systems.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">☁️</div>
                    <h3>Cloud Energy Management</h3>
                    <p>Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                </div>
            </div>
        </section>

        <!-- Energy Optimization Modal -->
        <div class="energy-optimization-modal" id="energyOptimizationModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">⚡</div>
                        <div class="modal-logo-text">Energy.AI</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChat()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item active">
                            <div class="chat-title">Energy Optimization</div>
                            <div class="chat-preview">How can I reduce my electricity bill?</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Solar Panel Analysis</div>
                            <div class="chat-preview">Best solar options for my home</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Smart Home Setup</div>
                            <div class="chat-preview">Energy-efficient smart devices</div>
                        </div>
                    </div>

                    <!-- Close button in sidebar -->
                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeEnergyOptimization()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <!-- Fixed Energy AI Title -->
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">⚡</div>
                        <h1>Energy AI</h1>
                    </div>

                    <div class="modal-header">
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentArea">
                            <!-- Welcome content -->
                            <div class="welcome-content" id="welcomeContent">
                                <h2 class="welcome-title">Welcome</h2>



                                <!-- Input in center when welcome is shown -->
                                <div class="modal-input-container center-input" id="centerInputContainer">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea
                                            class="modal-input-field"
                                            id="modalMessageInput"
                                            placeholder="اسأل عن أي شيء..."
                                            rows="1"
                                        ></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessage()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Messages will be added directly here -->
                        </div>
                    </div>

                    <!-- Input at bottom when chatting -->
                    <div class="modal-input-container bottom-input" id="bottomInputContainer" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea
                                class="modal-input-field"
                                id="modalMessageInputBottom"
                                placeholder="اسأل عن أي شيء..."
                                rows="1"
                            ></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottom()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>


                </div>
            </div>
        </div>

        

        <div id="design" class="section design-section">
            <h2>Our Design Approach</h2>
            <div class="design-gallery">
                <div class="design-card">
                    <div class="design-image">
                        <ion-icon name="home-outline"></ion-icon>
                    </div>
                    <h3>Smart Home Integration</h3>
                    <p>Seamless connection between your energy systems and smart home devices.</p>
                </div>

                <div class="design-card map-card">
                    <div class="design-image map-container">
                        <div id="embedded-map" class="embedded-map"></div>
                        <div class="map-overlay">
                            <h4>Energy.Ai Maps</h4>
                            <button class="map-expand-btn" id="expandMapBtn">
                                <ion-icon name="expand-outline"></ion-icon>
                                View Full Map
                            </button>
                        </div>
                    </div>
                    <h3>Energy.Ai Maps</h3>
                    <p>Interactive mapping interface with real-time energy data visualization and location-based analysis.</p>
                </div>
            </div>
        </div>

        <div id="contact" class="section contact-section">
            <div class="contact-header">
                <h2>Get In Touch</h2>
                <p class="contact-subtitle">Ready to transform your energy future? Let's start the conversation.</p>
            </div>

            <!-- Free Consultation Highlight Box -->
            <div class="consultation-highlight">
                <div class="consultation-content">
                    <div class="consultation-icon">
                        <ion-icon name="bulb-outline"></ion-icon>
                    </div>
                    <div class="consultation-text">
                        <h3>Get Your Free Energy Consultation</h3>
                        <p>Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.</p>
                        <ul class="consultation-benefits">
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Free energy audit and assessment</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Customized energy optimization plan</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>ROI analysis and cost savings projection</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="contact-container">
                <div class="contact-form-wrapper">
                    <div class="contact-form">
                        <h3>Send us a Message</h3>
                        <form id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        <span>Full Name</span>
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-company">
                                        <ion-icon name="business-outline"></ion-icon>
                                        <span>Company</span>
                                    </label>
                                    <input type="text" id="contact-company" placeholder="Your company name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        <span>Email Address</span>
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-phone">
                                        <ion-icon name="call-outline"></ion-icon>
                                        <span>Phone Number</span>
                                    </label>
                                    <input type="tel" id="contact-phone" placeholder="+962 XXX XXX XXX">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contact-subject">
                                    <ion-icon name="chatbubble-outline"></ion-icon>
                                    <span>Subject</span>
                                </label>
                                <select id="contact-subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="consultation" selected>Free Consultation</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="support">Technical Support</option>
                                    <option value="demo">Request Demo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="contact-message">
                                    <ion-icon name="document-text-outline"></ion-icon>
                                    <span>Message</span>
                                </label>
                                <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs and how we can help..." required></textarea>
                            </div>

                            <button type="submit" class="submit-btn" id="contactSubmitBtn">
                                <span class="btn-text">Send Message</span>
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                            <div id="contactFormStatus" class="form-status"></div>
                        </form>
                    </div>
                </div>

                <div class="contact-info-wrapper">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <p class="info-description">Connect with our energy experts today</p>

                        <div class="info-items">
                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="location-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Office Location</h4>
                                    <p>Amman, Jordan</p>
                                    <span>Middle East Headquarters</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <span>We reply within 24 hours</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="call-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Phone Number</h4>
                                    <p>+962 79 155 6430</p>
                                    <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="time-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Business Hours</h4>
                                    <p>Sunday - Thursday</p>
                                    <span>9:00 AM - 6:00 PM (GMT+3)</span>
                                </div>
                            </div>
                        </div>

                        <div class="contact-map">
                            <div class="map-placeholder">
                                <ion-icon name="map-outline"></ion-icon>
                                <p>Interactive Map</p>
                                <button class="map-btn" onclick="openContactMap()">View Location</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <h3>Energy.Ai Maps - Interactive View</h3>
                <button class="map-close-btn" id="closeMapModal">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div id="full-map" class="full-map"></div>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="footer-brand-icon"></div>
                <h2>Energy.Ai</h2>
                <p>Powering the future with intelligent solutions</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#service" id="footerServiceLink">Services</a></li>
                    <li><a href="#design">Design</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="faq.html">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><ion-icon name="logo-facebook"></ion-icon></a>
                    <a href="#" aria-label="Instagram"><ion-icon name="logo-instagram"></ion-icon></a>
                    <a href="#" aria-label="Twitter"><ion-icon name="logo-twitter"></ion-icon></a>
                </div>
            </div>
            <div class="footer-newsletter">
                <h3>Newsletter</h3>
                <p>Stay updated with our latest news</p>
                <div class="newsletter-form">
                    <input type="email" placeholder="Your Email Address" aria-label="Email for newsletter">
                    <button aria-label="Subscribe">
                        <ion-icon name="paper-plane-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Energy.AI. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <!-- Critical JavaScript - Load immediately -->
    <script>
        // Enhanced loading script with language support
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize welcome screen with language support
            initializeWelcomeScreen();

            // Hide loading screen after critical resources load
            setTimeout(() => {
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => loadingScreen.remove(), 800);
                }
            }, 3000); // Extended time to show the beautiful welcome screen
        });

        function initializeWelcomeScreen() {
            // Add language attributes for translation
            const welcomeTitle = document.querySelector('.welcome-title');
            const brandName = document.querySelector('.brand-name');
            const welcomeSubtitle = document.querySelector('.welcome-subtitle');

            // Language attributes removed - English only

            // Add additional energy particles for enhanced visual effect
            createEnergyParticles();
        }

        function createEnergyParticles() {
            const particlesContainer = document.querySelector('.energy-particles');
            if (particlesContainer) {
                // Create additional floating particles
                for (let i = 0; i < 6; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'floating-particle';
                    particle.style.cssText = `
                        position: absolute;
                        width: 3px;
                        height: 3px;
                        background: var(--secondary-color);
                        border-radius: 50%;
                        top: ${Math.random() * 100}%;
                        left: ${Math.random() * 100}%;
                        animation: particleFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                        animation-delay: ${Math.random() * 2}s;
                        opacity: 0.7;
                    `;
                    particlesContainer.appendChild(particle);
                }
            }
        }

        // Energy Optimization Modal Functions
        function openEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';

                // Ensure welcome content is visible when modal opens
                setTimeout(() => {
                    const welcomeContent = document.getElementById('welcomeContent');
                    const welcomeTitle = welcomeContent?.querySelector('.welcome-title');

                    if (welcomeContent) {
                        welcomeContent.style.display = 'flex';
                        welcomeContent.style.visibility = 'visible';
                        welcomeContent.style.opacity = '1';
                    }

                    if (welcomeTitle) {
                        welcomeTitle.style.display = 'block';
                        welcomeTitle.style.visibility = 'visible';
                        welcomeTitle.style.opacity = '1';
                    }
                }, 100);
            }
        }

        function closeEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }


        // Conversation memory for Energy Optimization modal
        let energyModalConversation = [];

        function startNewChat() {
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const chatContentArea = document.getElementById('chatContentArea');

            // Clear all existing messages from chat content area
            const existingMessages = chatContentArea.querySelectorAll('.message');
            existingMessages.forEach(message => message.remove());

            // Remove any existing messages container
            const messagesContainer = chatContentArea.querySelector('.messages-list');
            if (messagesContainer) {
                messagesContainer.remove();
            }

            // Remove any messages containers that might be hiding the welcome content
            const allMessagesContainers = chatContentArea.querySelectorAll('.messages-container');
            allMessagesContainers.forEach(container => container.remove());

            // Reset chat content area to initial state
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }

            // Reset conversation memory
            energyModalConversation = [];

            // Recreate welcome content if it was cleared by previous messages
            if (welcomeContent) {
                // Check if welcome content was cleared (no children or only messages-list)
                const hasWelcomeElements = welcomeContent.querySelector('.welcome-title');

                if (!hasWelcomeElements) {
                    // Clear any existing content
                    welcomeContent.innerHTML = '';

                    // Recreate welcome title
                    const welcomeTitle = document.createElement('h2');
                    welcomeTitle.className = 'welcome-title';
                    welcomeTitle.textContent = 'Welcome';
                    welcomeContent.appendChild(welcomeTitle);

                    // Recreate center input if it doesn't exist
                    const centerInputContainer = document.createElement('div');
                    centerInputContainer.className = 'modal-input-container center-input';
                    centerInputContainer.id = 'centerInputContainer';

                    centerInputContainer.innerHTML = `
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea
                                class="modal-input-field"
                                id="modalMessageInput"
                                placeholder="اسأل عن أي شيء..."
                                rows="1"
                            ></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessage()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    `;

                    welcomeContent.appendChild(centerInputContainer);
                }

                // Show welcome content with proper styling
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'center';
                welcomeContent.style.height = '100%';
                welcomeContent.style.position = 'relative';
                welcomeContent.style.visibility = 'visible';
                welcomeContent.style.opacity = '1';
                welcomeContent.style.zIndex = '1';
                welcomeContent.style.textAlign = 'center';
                welcomeContent.style.maxWidth = 'none';
            }

            // Show welcome elements with explicit styling
            const welcomeTitle = welcomeContent?.querySelector('.welcome-title');
            const centerInputFromWelcome = welcomeContent?.querySelector('.center-input');

            if (welcomeTitle) {
                welcomeTitle.style.display = 'block';
                welcomeTitle.style.visibility = 'visible';
                welcomeTitle.style.opacity = '1';
                welcomeTitle.style.fontSize = '36px';
                welcomeTitle.style.fontWeight = '700';
                welcomeTitle.style.marginBottom = '32px';
                welcomeTitle.style.textAlign = 'center';
                welcomeTitle.style.color = '#ffffff';
                welcomeTitle.style.textShadow = '0 4px 20px rgba(255, 255, 255, 0.4)';
            }

            // Show center input and hide bottom input
            if (centerInputFromWelcome) {
                centerInputFromWelcome.style.display = 'flex';
                centerInputFromWelcome.style.visibility = 'visible';
                centerInputFromWelcome.style.opacity = '1';
            }
            if (centerInput) {
                centerInput.style.display = 'flex';
                centerInput.style.visibility = 'visible';
                centerInput.style.opacity = '1';
            }
            if (bottomInput) {
                bottomInput.style.display = 'none';
            }

            // Clear both inputs
            const modalInput = document.getElementById('modalMessageInput');
            const modalInputBottom = document.getElementById('modalMessageInputBottom');
            if (modalInput) modalInput.value = '';
            if (modalInputBottom) modalInputBottom.value = '';
        }



        async function sendModalMessage() {
            const messageInput = document.getElementById('modalMessageInput');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            switchToChatMode();
            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        async function sendModalMessageFromBottom() {
            const messageInput = document.getElementById('modalMessageInputBottom');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        function updateBottomPadding() {
            const input = document.getElementById('bottomInputContainer');
            const scroller = document.querySelector('.modal-chat-area');
            const lists = document.querySelectorAll('.messages-list, .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacer();
        }

        function ensureInputSpacer() {
            try {
                const list = document.querySelector('.messages-list');
                const input = document.getElementById('bottomInputContainer');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacer');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacer';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                }
            } catch (e) {}
        }

        function switchToChatMode() {
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const chatContentArea = document.getElementById('chatContentArea');

            // Hide center input if it exists
            if (centerInput) {
                centerInput.style.display = 'none';
            }

            // Change welcome content styling for messages
            welcomeContent.style.textAlign = 'left';
            welcomeContent.style.maxWidth = '100%';
            welcomeContent.style.display = 'flex';
            welcomeContent.style.flexDirection = 'column';
            welcomeContent.style.alignItems = 'center';
            welcomeContent.style.justifyContent = 'flex-start';
            welcomeContent.style.height = 'auto';
            welcomeContent.style.paddingBottom = '0px';

            // Change chat area styling for messages
            chatContentArea.style.justifyContent = 'flex-start';
            chatContentArea.style.textAlign = 'left';
            chatContentArea.style.alignItems = 'stretch';
            chatContentArea.style.padding = '20px';

            // Show bottom input
            bottomInput.style.display = 'flex';

            // Ensure enough bottom space so last message stays above the input bar (dynamic)
            updateBottomPadding();
        }

        // Helper to scroll to bottom while keeping last message above the fixed input bar
        function scrollToBottomSafe() {
            const scroller = document.querySelector('.modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainer');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24; // small breathing space
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        // Predictive Maintenance Modal Functions (isolated)
        function openPredictiveMaintenance() {
            console.warn('Predictive Maintenance moved to standalone page');
            return;
            const modal = document.getElementById('predictiveMaintenanceModal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';

                setTimeout(() => {
                    const welcomeContent = document.getElementById('pmWelcomeContent');
                    const welcomeTitle = welcomeContent?.querySelector('.welcome-title');
                    if (welcomeContent) {
                        welcomeContent.style.display = 'flex';
                        welcomeContent.style.visibility = 'visible';
                        welcomeContent.style.opacity = '1';
                    }
                    if (welcomeTitle) {
                        welcomeTitle.style.display = 'block';
                        welcomeTitle.style.visibility = 'visible';
                        welcomeTitle.style.opacity = '1';
                    }
                }, 100);

                // Initialize PM sidebar hover handlers when opened
                initializePmSidebar();
            }
        }

        function closePredictiveMaintenance() {
            console.warn('Predictive Maintenance moved to standalone page');
            return;
            const modal = document.getElementById('predictiveMaintenanceModal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }

        let pmModalConversation = [];

        function startPmNewChat() {
            return;
            const welcomeContent = document.getElementById('pmWelcomeContent');
            const chatContentArea = document.getElementById('pmChatContentArea');

            // Clear existing messages
            const existing = chatContentArea.querySelectorAll('.message');
            existing.forEach(m => m.remove());
            const list = chatContentArea.querySelector('.messages-list');
            if (list) list.remove();

            // Reset visual state
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }

            pmModalConversation = [];

            if (welcomeContent) {
                const hasWelcome = welcomeContent.querySelector('.welcome-title');
                if (!hasWelcome) {
                    welcomeContent.innerHTML = '';
                    const t = document.createElement('h2');
                    t.className = 'welcome-title';
                    t.textContent = 'Welcome';
                    welcomeContent.appendChild(t);

                    const center = document.createElement('div');
                    center.className = 'modal-input-container center-input';
                    center.id = 'pmCenterInputContainer';
                    center.innerHTML = `
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea class="modal-input-field" id="pmModalMessageInput" placeholder="Ask anything about predictive maintenance..." rows="1"></textarea>
                            <button class="modal-send-btn" onclick="sendPmModalMessage()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>`;
                    welcomeContent.appendChild(center);
                }
            }
        }

        async function sendPmModalMessage() {
            return;
            const messageInput = document.getElementById('pmModalMessageInput');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;

            pmSwitchToChatMode();
            addPmModalMessage(message, 'user');
            try { pmScrollToBottomSafe(); pmUpdateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            try {
                const aiText = await requestPmAIResponse(message);
                addPmModalMessage(aiText, 'ai');
                pmUpdateBottomPadding();
            } catch (e) {
                addPmModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        async function sendPmModalMessageFromBottom() {
            return;
            const messageInput = document.getElementById('pmModalMessageInputBottom');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;

            addPmModalMessage(message, 'user');
            try { pmScrollToBottomSafe(); pmUpdateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try {
                const aiText = await requestPmAIResponse(message);
                addPmModalMessage(aiText, 'ai');
                pmUpdateBottomPadding();
            } catch (e) {
                addPmModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        function pmUpdateBottomPadding() {
            return;
            const input = document.getElementById('pmBottomInputContainer');
            const scroller = document.querySelector('#predictiveMaintenanceModal .modal-chat-area');
            const lists = document.querySelectorAll('#predictiveMaintenanceModal .messages-list, #predictiveMaintenanceModal .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            pmEnsureInputSpacer();
        }

        function pmEnsureInputSpacer() {
            return;
            try {
                const list = document.querySelector('#predictiveMaintenanceModal .messages-list');
                const input = document.getElementById('pmBottomInputContainer');
                if (!list || !input) return;
                let spacer = list.querySelector('#pmInputSpacer');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'pmInputSpacer';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                }
            } catch (e) {}
        }

        function pmSwitchToChatMode() {
            return;
            const welcomeContent = document.getElementById('pmWelcomeContent');
            const bottomInput = document.getElementById('pmBottomInputContainer');
            const chatContentArea = document.getElementById('pmChatContentArea');
            const centerInput = document.getElementById('pmCenterInputContainer');

            if (centerInput) centerInput.style.display = 'none';

            welcomeContent.style.textAlign = 'left';
            welcomeContent.style.maxWidth = '100%';
            welcomeContent.style.display = 'flex';
            welcomeContent.style.flexDirection = 'column';
            welcomeContent.style.alignItems = 'center';
            welcomeContent.style.justifyContent = 'flex-start';
            welcomeContent.style.height = 'auto';
            welcomeContent.style.paddingBottom = '0px';

            chatContentArea.style.justifyContent = 'flex-start';
            chatContentArea.style.textAlign = 'left';
            chatContentArea.style.alignItems = 'stretch';
            chatContentArea.style.padding = '20px';

            bottomInput.style.display = 'flex';
            pmUpdateBottomPadding();
        }

        function pmScrollToBottomSafe() {
            return;
            const scroller = document.querySelector('#predictiveMaintenanceModal .modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('pmBottomInputContainer');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24;
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        function addPmModalMessage(content, sender) {
            return;
            const welcomeContent = document.getElementById('pmWelcomeContent');
            const scroller = document.querySelector('#predictiveMaintenanceModal .modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200;
            })();

            if (!welcomeContent.querySelector('.messages-list')) {
                welcomeContent.innerHTML = '';
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '24px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';
                welcomeContent.appendChild(messagesContainer);
            }

            const messagesContainer = welcomeContent.querySelector('.messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.style.display = 'flex';
            messageDiv.style.gap = '0px';
            messageDiv.style.alignItems = 'center';
            messageDiv.style.justifyContent = 'center';

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';

            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);

            if (nearBottom && scroller) pmScrollToBottomSafe();
            pmUpdateBottomPadding();
            pmEnsureInputSpacer();
        }

        async function requestPmAIResponse(userMessage) {
            return '';
            pmModalConversation.push({ role: 'user', content: userMessage });
            let chatURL = '/api/chat';
            try { if (window.configSystem) chatURL = window.configSystem.getAPIEndpoint('chat'); } catch (e) {}
            const resp = await fetch(chatURL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages: pmModalConversation, temperature: 0.2, max_tokens: 600 })
            });
            if (!resp.ok) throw new Error('Bad response from AI backend');
            const data = await resp.json();
            const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';
            pmModalConversation.push({ role: 'assistant', content: ai });
            return ai;
        }

        function addModalMessage(content, sender) {
            const welcomeContent = document.getElementById('welcomeContent');
            const scroller = document.querySelector('.modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200; // auto-scroll only if user is near bottom
            })();

            // Replace welcome content with messages container on first message
            if (!welcomeContent.querySelector('.messages-list')) {
                // Clear welcome content
                welcomeContent.innerHTML = '';

                // Create messages container
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '24px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';

                welcomeContent.appendChild(messagesContainer);
            }

            const messagesContainer = welcomeContent.querySelector('.messages-list');

            // Create message
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.style.display = 'flex';
            messageDiv.style.gap = '0px';
            messageDiv.style.alignItems = 'center';
            messageDiv.style.justifyContent = 'center';



            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            // Basic formatting: escape HTML and preserve line breaks
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';

            messageDiv.appendChild(messageContent);

            // Add message to messages container
            messagesContainer.appendChild(messageDiv);

            if (nearBottom && scroller) {
                scrollToBottomSafe();
            }
            updateBottomPadding();
            ensureInputSpacer();
        }

        // Call backend AI for a smarter response
        async function requestAIResponse(userMessage) {
            // Maintain conversation context
            energyModalConversation.push({ role: 'user', content: userMessage });

            // Build endpoint from configSystem if available
            let chatURL = '/api/chat';
            try {
                if (window.configSystem) {
                    chatURL = window.configSystem.getAPIEndpoint('chat');
                }
            } catch (e) {}

            const resp = await fetch(chatURL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages: energyModalConversation, temperature: 0.3, max_tokens: 600 })
            });

            if (!resp.ok) {
                throw new Error('Bad response from AI backend');
            }
            const data = await resp.json();
            const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';

            // Save assistant reply to conversation
            energyModalConversation.push({ role: 'assistant', content: ai });

            return ai;
        }

        function generateModalResponse(userMessage) {
            const responses = {
                'energy bill': 'I can help you analyze your energy consumption! Please share your recent electricity bill, and I\'ll provide:\n\n• Peak usage patterns\n• Cost breakdown analysis\n• Optimization opportunities\n• Potential savings calculation\n\nYou can upload an image or PDF of your bill.',

                'solar': 'Great choice! Solar panels can significantly reduce your energy costs. Here\'s what I need to provide personalized recommendations:\n\n• Your location\n• Average monthly electricity bill\n• Roof size and orientation\n• Current energy usage patterns\n\nBased on this, I can calculate potential savings and ROI.',

                'smart home': 'Smart home devices can reduce energy consumption by 10-25%! Here are my top recommendations:\n\n🌡️ Smart Thermostats (15-20% savings)\n💡 Smart LED Bulbs (75% less energy)\n🔌 Smart Plugs (eliminate phantom loads)\n📱 Energy Monitoring Systems\n\nWhich area would you like to start with?',

                'costs': 'Here are proven ways to reduce your electricity costs:\n\n1. **Immediate Actions:**\n   • Switch to LED lighting\n   • Unplug unused devices\n   • Adjust thermostat settings\n\n2. **Medium-term:**\n   • Upgrade to efficient appliances\n   • Install smart thermostats\n   • Improve insulation\n\n3. **Long-term:**\n   • Solar panel installation\n   • Energy storage systems\n\nWould you like detailed guidance on any of these?'
            };

            for (const [key, response] of Object.entries(responses)) {
                if (userMessage.toLowerCase().includes(key)) {
                    return response;
                }
            }

            return 'I\'m here to help with all your energy optimization needs! I can assist with:\n\n• Energy bill analysis\n• Solar panel recommendations\n• Smart home solutions\n• Cost reduction strategies\n• Efficiency improvements\n\nWhat specific area would you like to explore?';
        }

        // Auto-resize textarea for modal
        document.addEventListener('DOMContentLoaded', function() {
            const modalMessageInput = document.getElementById('modalMessageInput');
            if (modalMessageInput) {
                modalMessageInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                    try { updateBottomPadding(); } catch (e) {}
                });

                modalMessageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendModalMessage();
                    }
                });

                // Bottom input event listeners
                const modalMessageInputBottom = document.getElementById('modalMessageInputBottom');
                if (modalMessageInputBottom) {
                    modalMessageInputBottom.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                        try { updateBottomPadding(); } catch (e) {}
                    });

                    modalMessageInputBottom.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendModalMessageFromBottom();
                        }
                    });
                }
            }
            try { updateBottomPadding(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPadding(); } catch (e) {} });
        });

        // Removed PM modal auto-resize (standalone page)
    </script>



    <!-- Auth system will be loaded by auth-system.js -->

    <!-- Glassmorphism System - Priority Load -->
    <script src="js/glassmorphism.js"></script>

    <!-- Core Systems - Load first -->
    <script src="js/config.js"></script>
    <script src="js/state-management.js"></script>
    <script src="js/security-system.js"></script>
    <script src="js/performance-optimizations.js"></script>
    <script src="js/notification-system.js"></script>
    <script src="js/analytics-system.js"></script>

    <!-- Core JavaScript -->
    <script src="js/jarvis-config.js"></script>
    <!-- Language system removed - English only -->
    <script src="js/auth-system.js"></script>
    <script src="js/embedded-map.js"></script>
    <script src="js/main.js"></script>

    <!-- Three.js and Neon Cursor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.139.2/build/three.module.js",
            "threejs-toys": "https://unpkg.com/threejs-toys@0.0.8/build/threejs-toys.module.cdn.min.js"
        }
    }
    </script>
    <script type="module" src="js/neon-cursor.js"></script>

    <!-- Sidebar hover functionality -->
    <script>
        // Sidebar hover functionality
        function initializeSidebar() {
            const sidebar = document.querySelector('.modal-sidebar');
            const hoverArea = document.querySelector('.sidebar-hover-area');
            let isHovered = false;
            let hoverTimeout;

            if (hoverArea && sidebar) {
                // Show sidebar on hover area enter
                hoverArea.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                    isHovered = true;
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.opacity = '1';
                    sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                });

                // Show sidebar on sidebar enter
                sidebar.addEventListener('mouseenter', () => {
                    clearTimeout(hoverTimeout);
                    isHovered = true;
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.opacity = '1';
                    sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                });

                // Hide sidebar with delay
                sidebar.addEventListener('mouseleave', () => {
                    isHovered = false;
                    hoverTimeout = setTimeout(() => {
                        if (!isHovered) {
                            const isMobile = window.innerWidth <= 768;
                            const translateX = isMobile ? '-290px' : '-270px';
                            sidebar.style.transform = `translateX(${translateX})`;
                            sidebar.style.opacity = '0';
                            sidebar.style.boxShadow = 'none';
                        }
                    }, 500);
                });

                // Handle window resize
                window.addEventListener('resize', () => {
                    if (!isHovered) {
                        const isMobile = window.innerWidth <= 768;
                        const translateX = isMobile ? '-290px' : '-270px';
                        sidebar.style.transform = `translateX(${translateX})`;
                        sidebar.style.opacity = '0';
                    }
                });
            }
        }

        // Initialize sidebar when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initializeSidebar();
        });

        // Removed PM sidebar hover (standalone page)
    </script>

    <!-- خلفية متحركة مع أشكال هندسية وتأثيرات متقدمة -->
    <div class="animated-background">
        <!-- الطبقة الأساسية للخلفية -->
        <div class="background-layer base-layer"></div>

        <!-- تأثير نبضة واضح -->
        <div class="pulse-effect"></div>

        <!-- الأشكال الهندسية الكبيرة -->
        <div class="geometric-shapes">
            <div class="main-geometric-shape"></div>
            <div class="secondary-shape"></div>
            <div class="tertiary-shape"></div>
            <div class="quaternary-shape"></div>
        </div>

        <!-- الأشكال المتحركة -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
            <div class="shape shape-7"></div>
            <div class="shape shape-8"></div>
        </div>

        <!-- خطوط الطاقة -->
        <div class="energy-lines">
            <div class="energy-line line-1"></div>
            <div class="energy-line line-2"></div>
            <div class="energy-line line-3"></div>
        </div>

        <!-- جسيمات متحركة -->
        <div class="particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
        </div>

        <!-- تأثيرات الضوء -->
        <div class="light-effects">
            <div class="light-beam beam-1"></div>
            <div class="light-beam beam-2"></div>
            <div class="light-beam beam-3"></div>
        </div>
    </div>
</body>
</html>
