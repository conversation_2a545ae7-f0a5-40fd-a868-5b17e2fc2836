/* Modern Glassmorphism UI System for Energy.AI */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* CSS Variables for Modern Glassmorphism */
:root {
    /* Primary Gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);

    /* Glass Effects */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --backdrop-blur: blur(20px);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);

    /* Container */
    --container-max-width: 1200px;
    --section-padding: 100px 20px;
    --text-accent: #00bcd4;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.6s ease;
    
    /* Spacing */
    --section-padding: 100px 20px;
    --container-max-width: 1200px;
    
    /* Border Radius */
    --border-radius-sm: 12px;
    --border-radius-md: 20px;
    --border-radius-lg: 30px;
}

/* Light Theme Variables */
[data-theme="light"] {
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --text-primary: #2c3e50;
    --text-secondary: #5d4e37;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-attachment: fixed;
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
    scroll-behavior: smooth;
}

/* Glassmorphism Components */
.glass {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-md);
    box-shadow: var(--glass-shadow), inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.glass:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Modern Glassmorphism Navigation - ثابتة في المنتصف */
.navbar {
    position: fixed !important;
    top: 20px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.12) !important;
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 50px;
    padding: 15px 30px;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    width: 90%;
    max-width: 1000px;
    /* منع أي تحريك أو تأثيرات */
    transition: none !important;
}

.navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
    border-radius: inherit;
    z-index: -1;
}

.nav-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 30px;
    width: 100%;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 12px;
    color: var(--text-primary);
    text-decoration: none;
    font-size: 1.3rem;
    font-weight: 700;
    padding: 8px 12px;
    border-radius: 20px;
}

/* تم إزالة تأثير hover للحفاظ على ثبات القائمة */

.nav-logo-icon {
    font-size: 1.8rem;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
}

.site-logo {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.site-logo::before {
    content: '⚡';
    font-size: 1.2rem;
    color: white;
    z-index: 2;
    position: relative;
}



.nav-menu {
    display: flex;
    list-style: none;
    gap: 25px;
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    padding: 10px 18px;
    border-radius: 25px;
}

.nav-menu a.active {
    color: var(--text-primary);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

/* تم إزالة تأثير hover للحفاظ على ثبات القائمة */

/* تم إزالة تأثيرات hover للقائمة للحفاظ على ثباتها */

.nav-actions {
    display: flex !important;
    align-items: center;
    gap: 15px;
    z-index: 100;
    position: relative;
}

/* Contact Button */
.contact-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 25px;
    padding: 10px 20px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* تم إزالة تأثير hover لزر الاتصال للحفاظ على ثبات القائمة */

.contact-btn i {
    font-size: 1rem;
}

.glass-button {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    cursor: pointer;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* تم إزالة تأثير hover للأزرار الزجاجية للحفاظ على ثبات القائمة */

.glass-button ion-icon {
    font-size: 1.2rem;
}



/* Theme Switch */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
}

.theme-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.2);
    transition: var(--transition-normal);
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 2px;
    background: white;
    transition: var(--transition-normal);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
    background: var(--secondary-color);
}

input:checked + .slider:before {
    transform: translateX(24px);
}

/* Desktop Navigation - Always visible */
@media (min-width: 769px) {
    .nav-menu {
        display: flex !important;
    }

    .mobile-menu-toggle {
        display: none !important;
    }
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .navbar {
        width: 95%;
        padding: 12px 20px;
    }

    .nav-content {
        gap: 15px;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--glass-bg);
        backdrop-filter: var(--backdrop-blur);
        border: 1px solid var(--glass-border);
        border-radius: var(--border-radius-md);
        margin-top: 10px;
        padding: 20px;
        flex-direction: column;
        gap: 15px;
        box-shadow: var(--shadow-glass);
        z-index: 1000;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-menu a {
        padding: 12px 20px;
        text-align: center;
        border-radius: 15px;
    }

    .nav-logo {
        font-size: 1.1rem;
        gap: 8px;
    }

    .nav-logo-icon {
        font-size: 1.5rem;
    }

    .contact-btn {
        display: none;
    }



    .glass-button {
        width: 35px;
        height: 35px;
    }

    .glass-button ion-icon {
        font-size: 1rem;
    }
}

/* Mobile Menu Toggle Button */
.mobile-menu-toggle {
    display: none;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    width: 35px;
    height: 35px;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    cursor: pointer;
}

@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: flex;
    }
}



/* Hero Animations */
@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Auth system styles are in auth-system.js */

/* Official English Website - Clean and Professional */

/* English-Only Website Styling */
html[lang="en"] {
    direction: ltr;
    text-align: left;
}

body.lang-en {
    font-family: 'Roboto', 'Inter', sans-serif;
}

/* Modern Glassmorphism Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 160px 20px 80px;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.3));
}

.hero-content {
    max-width: 900px;
    z-index: 10;
    position: relative;
    text-align: center;
    padding: 0 20px;
}

.hero-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    margin-bottom: 30px;
    line-height: 1.2;
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 10px;
    letter-spacing: -1px;
}

.title-line-1 {
    color: #ffffff;
    text-shadow: 0 6px 30px rgba(255, 255, 255, 0.4);
    font-weight: 700;
    transform: translateY(0);
    animation: slideInFromTop 1s ease-out;
}

.title-line-2 {
    background: linear-gradient(135deg, #ff6b35, #f7931e, #ffaa00);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 6px 30px rgba(255, 107, 53, 0.6);
    font-weight: 800;
    transform: translateY(0);
    animation: slideInFromTop 1s ease-out 0.2s both;
    position: relative;
}

.title-line-2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 4px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 2px;
    opacity: 0.7;
}

.title-line-3 {
    color: #ffffff;
    font-size: clamp(2rem, 4vw, 3rem);
    margin-top: 20px;
    text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4);
    font-weight: 700;
    transform: translateY(0);
    animation: slideInFromTop 1s ease-out 0.4s both;
}

.hero .highlight {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.hero-description {
    font-size: 1.1rem;
    color: rgba(255, 255, 255, 0.85);
    margin-bottom: 40px;
    line-height: 1.6;
    max-width: 600px;
    text-align: center;
    font-weight: 400;
    letter-spacing: 0.5px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.6s both;
}

.hero-actions {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 40px;
    animation: fadeInUp 1s ease-out 0.8s both;
}

/* Buttons */
.btn {
    padding: 14px 28px;
    border: none;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #ff6b35, #f7931e, #ffaa00);
    color: white;
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.3);
    border-radius: 50px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5);
    background: linear-gradient(135deg, #f7931e, #ff6b35, #ffaa00);
}

.btn-primary ion-icon {
    font-size: 1.1rem;
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    padding: 14px 28px;
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.3px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.15);
}

.btn-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: var(--glass-blur);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.glass-button-primary {
    background: var(--accent-gradient);
    color: white;
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    display: inline-flex;
    align-items: center;
    gap: 10px;
    transition: var(--transition);
    box-shadow: 0 8px 32px rgba(79, 172, 254, 0.3);
    border: none;
    cursor: pointer;
}

.glass-button-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(79, 172, 254, 0.4);
}

/* Floating Glass Elements */
.floating-glass {
    position: absolute;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-glass);
}

.floating-glass-1 {
    width: 200px;
    height: 200px;
    top: 20%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.floating-glass-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation: float 8s ease-in-out infinite reverse;
}

.floating-glass-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation: float 10s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
}

/* Hero Visual */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.energy-visualization {
    width: 300px;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border-radius: 50%;
}

.energy-icon {
    font-size: 4rem;
    z-index: 2;
    animation: energyPulse 2s ease-in-out infinite;
}

@keyframes energyPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.energy-waves {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.wave {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 100px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: waveExpand 3s ease-out infinite;
}

.wave:nth-child(2) {
    animation-delay: 1s;
}

.wave:nth-child(3) {
    animation-delay: 2s;
}

@keyframes waveExpand {
    0% {
        width: 100px;
        height: 100px;
        opacity: 1;
    }
    100% {
        width: 300px;
        height: 300px;
        opacity: 0;
    }
}

/* Modern Glassmorphism Sections */
.section {
    padding: 100px 20px;
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
}

.section h2 {
    font-size: 3rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 60px;
    background: var(--accent-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 50px;
    line-height: 1.7;
}

/* Glass Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 60px;
}

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 40px 30px;
    text-align: center;
    transition: var(--transition);
    box-shadow: var(--shadow-glass);
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--accent-gradient);
    opacity: 0;
    transition: var(--transition);
}

.glass-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.glass-card:hover::before {
    opacity: 1;
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.glass-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact Form */
.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--shadow-glass);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 16px;
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    color: var(--text-primary);
    font-size: 16px;
    transition: var(--transition);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: rgba(255, 107, 53, 0.5);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.2);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Footer */
.footer {
    background: var(--glass-bg);
    backdrop-filter: var(--backdrop-blur);
    border-top: 1px solid var(--glass-border);
    padding: 60px 20px 30px;
    text-align: center;
    margin-top: 100px;
}

.footer h3 {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.footer p {
    color: var(--text-secondary);
    margin-bottom: 30px;
}

.footer-bottom {
    border-top: 1px solid var(--glass-border);
    padding-top: 20px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-content {
        padding: 0 20px;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3rem);
        gap: 8px;
        margin-bottom: 25px;
    }

    .title-line-3 {
        font-size: clamp(1.5rem, 6vw, 2rem);
        margin-top: 15px;
    }

    .hero-description {
        font-size: 1rem;
        margin-bottom: 35px;
        line-height: 1.5;
    }

    .hero-actions {
        gap: 15px;
        flex-direction: column;
        align-items: center;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        max-width: 250px;
        padding: 12px 20px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 0 15px;
    }

    .hero-title {
        font-size: clamp(1.8rem, 10vw, 2.5rem);
        letter-spacing: -0.5px;
    }

    .title-line-3 {
        font-size: clamp(1.3rem, 8vw, 1.8rem);
    }

    .hero-description {
        font-size: 0.9rem;
        letter-spacing: 0.2px;
    }
}

    .nav-menu {
        display: none;
    }

    .navbar {
        padding: 12px 20px;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .floating-glass {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .section h2 {
        font-size: 2rem;
    }

    .glass-button-primary {
        padding: 14px 24px;
        font-size: 1rem;
    }
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 4px 20px rgba(255, 107, 53, 0.3);
}

.feature-card h3 {
    font-size: 1.3rem;
    margin-bottom: 15px;
    color: var(--text-primary);
    font-weight: 600;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }
    
    .hero-visual {
        order: -1;
    }
}

@media (max-width: 768px) {
    .navbar {
        width: 95%;
        padding: 12px 20px;
    }
    
    .menu ul {
        display: none;
    }
    
    .nav-actions {
        gap: 15px;
    }
    
    .hero {
        padding: 80px 20px;
    }
    
    .section {
        padding: 80px 20px;
    }
    
    .section-container {
        padding: 30px 20px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .hero h1 {
        font-size: 2rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .energy-visualization {
        width: 250px;
        height: 250px;
    }
}

/* Additional Glassmorphism Effects */
.glass-strong {
    background: rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* Services Section */
.services-section {
    padding: var(--section-padding);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 40px;
}

.service-card {
    padding: 35px 25px;
    text-align: center;
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 114, 0, 0.08), transparent);
    transition: left 0.6s;
}

.service-card:hover::before {
    left: 100%;
}

.service-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 0 1px rgba(255, 114, 0, 0.1);
}

.service-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 18px;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: white;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.25);
    position: relative;
    overflow: hidden;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: iconShine 2s ease-in-out infinite;
}

@keyframes iconShine {
    0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.service-card h3 {
    font-size: 1.3rem;
    margin-bottom: 12px;
    color: var(--text-primary);
    font-weight: 600;
}

.service-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Contact Section */
.contact-section {
    padding: var(--section-padding);
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 40px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.95rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 14px 18px;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-sm);
    color: var(--text-primary);
    font-size: 0.95rem;
    transition: all var(--transition-normal);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #ff6b35;
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-secondary);
}

/* Footer */
.footer {
    padding: 40px 20px;
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.footer-content {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 30px;
}

.footer p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Loading Screen Enhancements */
.loading-screen {
    background: var(--dark-bg);
    backdrop-filter: blur(20px);
}

[data-theme="light"] .loading-screen {
    background: var(--light-bg);
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-on-scroll {
    opacity: 0;
    animation: fadeInUp 0.8s ease-out forwards;
}

/* Improved Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #ff6b35, #f7931e);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #f7931e, #ffaa00);
}

/* Fixed CTA Button */
.fixed-cta-container {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fixed-cta-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 50px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    cursor: pointer;
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
}

.fixed-cta-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

/* AI Chat Interface */
.ai-chat-container {
    position: fixed;
    bottom: 100px;
    right: 30px;
    width: 350px;
    height: 500px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow);
    z-index: 999;
    display: none;
    flex-direction: column;
    overflow: hidden;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.chat-header h3 {
    color: var(--text-primary);
    margin: 0;
    font-size: 1.1rem;
}

.chat-controls {
    display: flex;
    gap: 10px;
}

.chat-control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.chat-control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

/* RTL Support for Arabic */
[dir="rtl"] .nav-content {
    flex-direction: row-reverse;
}

[dir="rtl"] .hero-content {
    text-align: right;
}

[dir="rtl"] .menu ul {
    flex-direction: row-reverse;
}



/* Print Styles */
@media print {
    .navbar,
    .floating-shapes,
    .glassmorphism-bg {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .glass {
        background: white;
        border: 1px solid #ddd;
        box-shadow: none;
    }
}

/* Predictive Maintenance Modal Styles */
.predictive-maintenance-modal .modal-message {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 20px;
    padding: 16px;
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.predictive-maintenance-modal .modal-message.user {
    background: linear-gradient(135deg, rgba(255, 114, 0, 0.1), rgba(255, 114, 0, 0.05));
    border-color: rgba(255, 114, 0, 0.2);
    margin-left: 60px;
    flex-direction: row-reverse;
}

.predictive-maintenance-modal .modal-message.bot {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border-color: rgba(255, 255, 255, 0.1);
    margin-right: 60px;
}

.predictive-maintenance-modal .ai-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ff7200, #ff9500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(255, 114, 0, 0.3);
}

.predictive-maintenance-modal .message-content {
    flex: 1;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

.predictive-maintenance-modal .message-time {
    font-size: 11px;
    color: var(--text-secondary);
    margin-top: 8px;
    opacity: 0.7;
}

/* Typing Indicator for Predictive Maintenance */
.predictive-maintenance-modal .typing-indicator .typing-dots {
    display: flex;
    gap: 4px;
    align-items: center;
}

.predictive-maintenance-modal .typing-dots span {
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #ff7200, #ff9500);
    border-radius: 50%;
    animation: pmTypingDots 1.4s infinite ease-in-out;
}

.predictive-maintenance-modal .typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.predictive-maintenance-modal .typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes pmTypingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Predictive Maintenance Modal Sidebar Customization */
.predictive-maintenance-modal .modal-logo-icon {
    background: linear-gradient(135deg, #ff7200, #ff9500);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    box-shadow: 0 4px 15px rgba(255, 114, 0, 0.3);
}

.predictive-maintenance-modal .modal-logo-text {
    background: linear-gradient(135deg, #ff7200, #ff9500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 18px;
}

.predictive-maintenance-modal .fixed-energy-title .energy-title-icon {
    background: linear-gradient(135deg, #ff7200, #ff9500);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 6px 20px rgba(255, 114, 0, 0.4);
}

.predictive-maintenance-modal .fixed-energy-title h1 {
    background: linear-gradient(135deg, #ff7200, #ff9500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 800;
    font-size: 2.5rem;
}

/* Predictive Maintenance Input Styling */
.predictive-maintenance-modal .modal-send-btn {
    background: linear-gradient(135deg, #ff7200, #ff9500);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 114, 0, 0.3);
}

.predictive-maintenance-modal .modal-send-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 114, 0, 0.4);
}

.predictive-maintenance-modal .modal-input-field:focus {
    border-color: rgba(255, 114, 0, 0.5);
    box-shadow: 0 0 0 3px rgba(255, 114, 0, 0.1);
}
